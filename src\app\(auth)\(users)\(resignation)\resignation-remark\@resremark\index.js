'use client';
import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { identifiers } from '@/helper/constants/identifier';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import dayjs from 'dayjs';
import ResignationDetails from '@/components/Resignation/ResignationDetails/ResignationDetails';
import { updateResignationRemark } from '@/services/resignationService';

export default function ResRemark({
  setToggleModal,
  toggleModal,
  singleList,
  handleResignationRemak,
  isLoader,
  authState,
}) {
  const [remarkStatus, setRemarkStatus] = useState();
  const handleClose = () => {
    setToggleModal(!toggleModal);
  };
  useEffect(() => {
    singleList?.resignation_status &&
      setRemarkStatus(singleList?.resignation_status);
  }, [singleList?.resignation_status]);

  const handleSubmit = async (values) => {
    try {
      const payload = {
        ...(remarkStatus === 'accepted' && {
          last_serving_date: values?.lastworkingday,
        }),
        resignation_status: values?.status,
        remarks: values?.remark,
      };
      const response = await updateResignationRemark(singleList?.id, payload);
      if (response.success) {
        handleResignationRemak(payload, singleList?.id);
      }
    } catch (error) {
      console.error('Error updating resignation remark:', error);
    }
  };

  return (
    <Box>
      <Formik
        initialValues={{
          status:
            singleList?.resignation_status &&
            singleList?.resignation_status !== 'pending'
              ? singleList?.resignation_status
              : '',
          remark: singleList?.approved_by_reason
            ? singleList?.approved_by_reason
            : '',
          lastworkingday: '',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          status: Yup.string().trim().required('This field is required'),
          remark: Yup.string().trim().required('This field is required'),
          lastworkingday: Yup.string().when('status', {
            is: 'accepted',
            then: (schema) => schema.required('This field is required'),
            otherwise: (schema) => schema.notRequired(),
          }),
        })}
        onSubmit={handleSubmit}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
        }) => (
          <Form onSubmit={handleSubmit}>
            <ResignationDetails
              resignationDetails={singleList}
              customClassName="res-remark-view"
            />
            {authState?.UserPermission?.resignation === 2 &&
              (singleList?.resignation_status === 'pending' ||
                singleList?.resignation_status === 'in-discussion') && (
                <>
                  <Box className="select-box pt8">
                    <CustomSelect
                      required
                      placeholder="Status"
                      options={identifiers?.RESIGNATION_OPTION}
                      value={
                        identifiers?.RESIGNATION_OPTION?.find((item) => {
                          return item?.value === values?.status;
                        }) || ''
                      }
                      name="status"
                      onChange={(e) => {
                        setFieldValue('status', e?.value);
                        setRemarkStatus(e?.value);
                      }}
                      label="Status"
                      error={Boolean(touched.status && errors.status)}
                      helperText={touched.status && errors.status}
                    />
                  </Box>
                  {remarkStatus === 'accepted' && (
                    <Box className="pt8">
                      <CustomDatePicker
                        required
                        label="Employ serving day"
                        name="lastworkingday"
                        value={dayjs(values?.lastworkingday)}
                        onBlur={handleBlur}
                        onChange={(date) => {
                          setFieldValue('lastworkingday', date);
                        }}
                        error={Boolean(
                          touched?.lastworkingday && errors?.lastworkingday
                        )}
                        helperText={
                          touched?.lastworkingday && errors?.lastworkingday
                        }
                        inputVariant="outlined"
                        disablePast
                      />
                    </Box>
                  )}

                  <Box className="pt8">
                    <CustomTextField
                      fullWidth
                      id="remark"
                      name="remark"
                      multiline
                      rows={3}
                      onChange={handleChange}
                      placeholder="Remark"
                      value={values?.remark}
                      error={Boolean(touched.remark && errors.remark)}
                      helperText={touched.remark && errors.remark}
                      label="Remark"
                      required
                    />
                  </Box>

                  <Box className="form-actions-btn mt16">
                    <CustomButton
                      type="submit"
                      variant="outlined"
                      title="Cancel"
                      onClick={handleClose}
                    />
                    <CustomButton
                      fullWidth
                      type="submit"
                      variant="contained"
                      title={isLoader ? 'Updating...' : 'Update'}
                      disabled={isLoader || !dirty}
                    />
                  </Box>
                </>
              )}
          </Form>
        )}
      </Formik>
    </Box>
  );
}
