import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import RequestChangesPage from '@/components/ChangeRequest';

export const metadata = generateMetadata({
  pageTitle: 'ChangeRequest',
});

const ChangeRequestPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <RequestChangesPage params={params} />
    </Box>
  );
};

export default ChangeRequestPage;
