'use client';
import React, { useState, useEffect, useContext } from 'react';
import { Box, Tab } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import OrganizationForm from '@/components/OrganizationDetails/OrganizationForm';
import { useParams, useRouter } from 'next/navigation';
import PlanUpdate from '@/components/PaymentMethod/PlanUpdate/index';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import AuthContext from '@/helper/authcontext';
import '../../../../org/(organizations)/organization/organization.scss';
import '../organization.scss';

export default function OrganizationEdit() {
  const router = useRouter();
  const { setOrgDetails } = useContext(AuthContext);
  const { id } = useParams();
  const [tab, setTab] = useState(1);
  const [planData, setPlanData] = useState();
  const [OrgData, setOrgData] = useState();
  const reports_tabs = [
    { id: 1, name: 'Organizations' },
    { id: 2, name: 'Plans' },
  ];
  const tabChangeHandler = (event, newValue) => {
    setTab(newValue);
  };
  const getPlanbyID = async (id) => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ORGANIZATION_BY_ID_PLAN + `${id}`
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          setPlanData(data?.data);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getOrgbyID = async (id) => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ORGANIZATION_BY_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          setOrgData(data?.data);
          setOrgDetails(data?.data);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    if (id) {
      getPlanbyID(id);
      getOrgbyID(id);
    }
  }, [id]);
  return (
    <Box className="main-page-container">
      <Box className="organization-wrap">
        <ArrowBackIosIcon
          className="cursor-pointer mt4"
          onClick={() => {
            router.push(`/sorg/organization`);
          }}
        />
        <TabContext value={String(tab)}>
          <Box className="tabs-wrap">
            <Box className="report-tabs">
              <TabList
                variant="scrollable"
                scrollButtons="auto"
                onChange={tabChangeHandler}
                aria-label="action tabs"
                className="tab-list-sec"
              >
                {reports_tabs?.map((obj, index) => {
                  return (
                    <Tab
                      key={index}
                      label={obj?.name}
                      value={String(obj?.id)}
                      className="tab-name"
                    />
                  );
                })}
              </TabList>
            </Box>
          </Box>
          <TabPanel value="1" className="pl0 pr0 pb0">
            <OrganizationForm OrgId={id} superAdmin={true} />
          </TabPanel>
          <TabPanel value="2" className="pl0 pr0 pb0">
            <PlanUpdate planData={planData} OrgData={OrgData} />
          </TabPanel>
        </TabContext>
      </Box>
    </Box>
  );
}
